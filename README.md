## 预警系统

- 集成 mybatis-plus(v3.4.3.1) 数据库开发包 （需修改mysql配置）参考：api#IDictService
- 提供 mybatis-plus 代码生成器，参考：repository#CodeGenerator
- 集成 xxl-job 任务调度 （需修改xxl-job配置）
- 集成组件 structure-swagger,提供 统一的api文档服务
- 集成组件 structure-redis,提供redis相关操作 （需修改redis配置）
- 集成组件 structure-lock,提供 基于注解的分布式锁服务
- 集成组件 structure-http,提供 基于网络相关的操作
- 添加组件 structure-notice,提供 通用的钉钉预警（可以配置多个，防止被限流）
- 集成组件 structure-log,提供 通用的日志打印和链路记录
- 集成组件 structure-secure,提供 统一的基于注解方式的接口授权服务 （可以不使用,不影响项目执行）参考：api#SecureController
- 提供统一的响应体结构,参考：api#WrapController
- 默认开启统一的全局异常处理，（可关闭,可自定义）参考 MessApiExceptionHandler

### 模块说明：

├── api -- api接口

├── common -- 通用常量、工具等

├── env -- 环境、配置（避免api模块和job模块重复配置）

├── job -- 任务调度

├── common-biz -- 通用服务（对 Service 层通用能力的下沉，如DAO层的Service、缓存方案、中间件通用处理、对第三方平台封装的层）

├── repository -- 数据库层(存放 mapper;DO;BO;DTO等)

> [分支规范](https://pm.qmqb.top/doc-objectLibs-custom-0-894-5226.html)<br/>
> [版本规范](https://pm.qmqb.top/doc-objectLibs-custom-0-894-5227.html)<br/>

### v1.0.0.0.2025040201
    1.预警事件管理，预警模型管理，数据源管理

### v1.0.0.1.2025090201
    1、构建统计类SDK，支持业务系统接入统计类数据；
    2、搭建规则引擎、规则匹配、消息通知、消息分发的基础功能；
    3、同步钉钉通信录获取用户信息，实现将预警消息通过短信和钉钉群指定用户发送；
    4、实现资方放款失败率预警；
