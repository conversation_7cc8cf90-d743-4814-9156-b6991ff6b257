create table t_alert_message
(
    id                 bigint auto_increment comment '主键ID'
        primary key,
    event_id           varchar(20)                        not null comment '预警事件编号，SDK生成（对应t_alert_event.event_id）',
    model_code         varchar(10)                        not null comment '模型编码（对应t_alert_model.model_code）',
    model_name         varchar(100)                       not null comment '预警名称（对应t_alert_model.model_name）',
    alert_type         varchar(10)                        null comment '预警类型（对应t_alert_model.alert_type）',
    platform_name      varchar(100)                       null comment '第三方名称（对应t_alert_event.platform_name）',
    period varchar (10) null comment '期次（对应t_alert_event.period）',
    service_no         varchar(100)                       null comment '业务唯一编号（对应t_alert_event.service_no）',
    business_type      varchar(20)                        null comment '业务类型（对应t_alert_model.business_type）',
    state              varchar(50)                        null comment '状态值， 例如：单状态/还款状态/ 支付状态/推送状态（对应t_alert_event.state）',
    index_value        varchar(100)                       null comment '指标值（对应t_alert_event.index_value）',
    reason             varchar(255)                       null comment '原因（对应t_alert_event.reason）',
    payload            text                               null comment '拓展字段（对应t_alert_event.payload）',
    rule_id            bigint                             null comment '规则id（对应t_alert_model.rule_id）',
    rule_name          varchar(2000)                       null comment '预警规则（对应t_rules.rule_name）',
    related            bit      default b'0'              null comment '是否关联规则 0为不关联，1为关联（对应t_alert_model.related）',
    warn_level         varchar(20)                        null comment '预警级别（对应t_alert_message.warn_level）',
    system_code        varchar(20)                        null comment '系统编号（对应t_data_source.system_code）',
    system_name        varchar(100)                       null comment '系统名称（对应t_data_source.system_name）',
    alert_time         datetime                           not null comment '预警事件时间/上报时间',
    config_id          bigint                             null comment '预警配置id（对应t_alert_model_config.id）',
    frequency          int(1)                             null comment '预警频率 0为不限制（对应t_alert_model_config.frequency）',
    warn_type          int(1)                             null comment '通知方式 1:钉钉，2:短信，3:钉钉+短信（对应t_alert_model_config.warn_type）',
    notification_users varchar(1024)                      null comment '通知人员（从t_alert_model_notifiers关联查询得出）',
    create_time        datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time        datetime default CURRENT_TIMESTAMP null comment '修改时间',
    creator            varchar(64)                        null comment '创建人',
    updater            varchar(64)                        null comment '更新人',
    deleted            bit      default b'0'              null comment '是否删除 0未删除 1已删除'
)
    comment '预警消息表' charset = utf8mb4;

create index idx_alert_time
    on t_alert_message (alert_time);

create index idx_model_code
    on t_alert_message (model_code);

create index idx_model_name
    on t_alert_message (model_name);

create index idx_platform_name
    on t_alert_message (platform_name);

create index idx_warn_level
    on t_alert_message (warn_level);

ALTER TABLE `t_alert_model`
    MODIFY COLUMN `related` bit(1) NULL DEFAULT 0 COMMENT '是否关联规则 0为不关联，1为关联' AFTER `alert_type`,
    ADD COLUMN `warned` bit(1) NULL DEFAULT 0 COMMENT '是否生成预警消息 0为不生成，1为生成' AFTER `congfig_id`;

alter table t_alert_event
    add related bit default 0 null comment '是否关联规则 0为不关联，1为关联' after problem_result;

ALTER TABLE `t_alert_model`
DROP COLUMN `rule_id`;

CREATE TABLE `t_alert_model_notifiers` (
                                           `user_id` bigint(20) NOT NULL COMMENT '通知用户ID',
                                           `config_id` bigint(20) NOT NULL COMMENT '预警配置ID',
                                           PRIMARY KEY (`user_id`,`config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预警通知人员表';

CREATE TABLE `t_alert_model_config` (
                                        `id` bigint(20) NOT NULL COMMENT '预警配置id',
                                        `model_id` bigint(20) DEFAULT NULL COMMENT '模型ID',
                                        `frequency` int(1) DEFAULT NULL COMMENT '预警频率 0为不限制',
                                        `warn_type` int(1) DEFAULT NULL COMMENT '预警方式 1:钉钉，2:短信，3:钉钉+短信',
                                        `warn_content` text COMMENT '预警内容',
                                        `warn_level` varchar(20) DEFAULT 'LOW' COMMENT '预警级别 HIGH:高,MIDDLE:中，LOW:低',
                                        `webhook` text COMMENT '钉钉群地址',
                                        `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                        `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
                                        `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
                                        `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除 0未删除 1已删除',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预警配置表';

CREATE TABLE `t_alert_model_rule` (
                                      `rule_id` bigint(20) NOT NULL COMMENT '规则ID',
                                      `model_id` bigint(20) NOT NULL COMMENT '模型ID',
                                      PRIMARY KEY (`model_id`,`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='模型规则关联表';


CREATE TABLE `t_capital` (
                             `id` bigint(20) NOT NULL,
                             `capital_name` varchar(50) DEFAULT NULL COMMENT '资方名称',
                             `capital_code` varchar(50) DEFAULT NULL COMMENT '资方编码',
                             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                             `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
                             `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
                             `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除 0未删除 1已删除',
                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资方表';

CREATE TABLE `t_rules` (
                           `id` bigint(20) NOT NULL COMMENT '规则id',
                           `rule_name` varchar(200) DEFAULT NULL COMMENT '规则名称',
                           `rule_code` varchar(50) DEFAULT NULL COMMENT '规则编码',
                           `rule_status` char(1) DEFAULT NULL COMMENT '规则状态：0-启动 1-关闭',
                           `rule_matching` char(1) DEFAULT NULL COMMENT '规则匹配：0-满足任一条件 1-满足所有条件',
                           `warn_level` varchar(20) DEFAULT NULL COMMENT '预警级别',
                           `apply_investor` text COMMENT '应用资方(多个资方之间逗号隔开)',
                           `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                           `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                           `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
                           `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
                           `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除 0未删除 1已删除',
                           PRIMARY KEY (`id`),
                           UNIQUE KEY `rule_name` (`rule_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='规则表';

CREATE TABLE `t_warn_condition` (
                                    `id` bigint(20) NOT NULL COMMENT '条件id',
                                    `rules_id` bigint(20) DEFAULT NULL COMMENT '规则id',
                                    `setting_item` varchar(50) DEFAULT NULL COMMENT '设置项',
                                    `operator` varchar(50) DEFAULT NULL COMMENT '运算符',
                                    `assignment_item` decimal(10,2) DEFAULT NULL COMMENT '赋值项',
                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                    `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
                                    `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
                                    `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除 0未删除 1已删除',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预警条件表';

-- 字典：预警级别
INSERT INTO `t_sys_dict_key` (`id`, `key_name`, `key_label`, `status`, `description`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952566234698129410, 'warn_level', '预警级别', b'1', '预警级别', '2025-08-05 11:04:02', '2025-08-05 11:04:02', NULL, NULL, b'0');
INSERT INTO `t_sys_dict_value` (`id`, `key_id`, `key_name`, `status`, `value`, `label`, `description`, `sort`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952566682532356098, 1952566234698129410, 'warn_level', b'1', 'HIGH', '高', '', NULL, '2025-08-05 11:05:49', '2025-08-05 11:06:19', NULL, NULL, b'0');
INSERT INTO `t_sys_dict_value` (`id`, `key_id`, `key_name`, `status`, `value`, `label`, `description`, `sort`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952566911180644353, 1952566234698129410, 'warn_level', b'1', 'MIDDLE', '中', '', NULL, '2025-08-05 11:06:43', '2025-08-05 11:06:43', NULL, NULL, b'0');
INSERT INTO `t_sys_dict_value` (`id`, `key_id`, `key_name`, `status`, `value`, `label`, `description`, `sort`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952567020505178113, 1952566234698129410, 'warn_level', b'1', 'LOW', '低', '', NULL, '2025-08-05 11:07:09', '2025-08-05 11:07:09', NULL, NULL, b'0');

-- 运算符
INSERT INTO `t_sys_dict_key` (`id`, `key_name`, `key_label`, `status`, `description`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952616589951655938, 'condition_operator', '运算符', b'1', '运算符', '2025-08-05 14:24:08', '2025-08-05 14:24:08', NULL, NULL, b'0');
INSERT INTO `t_sys_dict_value` (`id`, `key_id`, `key_name`, `status`, `value`, `label`, `description`, `sort`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952616674219417602, 1952616589951655938, 'condition_operator', b'1', 'GT', '大于', '', NULL, '2025-08-05 14:24:28', '2025-08-05 14:24:28', NULL, NULL, b'0');
INSERT INTO `t_sys_dict_value` (`id`, `key_id`, `key_name`, `status`, `value`, `label`, `description`, `sort`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952616855404961794, 1952616589951655938, 'condition_operator', b'1', 'GTE', '大于等于', '', NULL, '2025-08-05 14:25:11', '2025-08-05 14:25:11', NULL, NULL, b'0');
INSERT INTO `t_sys_dict_value` (`id`, `key_id`, `key_name`, `status`, `value`, `label`, `description`, `sort`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952616911067570177, 1952616589951655938, 'condition_operator', b'1', 'EQ', '等于', '', NULL, '2025-08-05 14:25:24', '2025-08-05 14:25:24', NULL, NULL, b'0');
INSERT INTO `t_sys_dict_value` (`id`, `key_id`, `key_name`, `status`, `value`, `label`, `description`, `sort`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952616972006612993, 1952616589951655938, 'condition_operator', b'1', 'LT', '小于', '', NULL, '2025-08-05 14:25:39', '2025-08-05 14:25:39', NULL, NULL, b'0');
INSERT INTO `t_sys_dict_value` (`id`, `key_id`, `key_name`, `status`, `value`, `label`, `description`, `sort`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952617042043101186, 1952616589951655938, 'condition_operator', b'1', 'LTE', '小于等于', '', NULL, '2025-08-05 14:25:55', '2025-08-05 14:25:55', NULL, NULL, b'0');
INSERT INTO `t_sys_dict_value` (`id`, `key_id`, `key_name`, `status`, `value`, `label`, `description`, `sort`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952617110489948162, 1952616589951655938, 'condition_operator', b'1', 'NE', '不等于', '', NULL, '2025-08-05 14:26:12', '2025-08-05 14:26:12', NULL, NULL, b'0');

-- 放款失败率预警
INSERT INTO `t_sys_dict_key` (`id`, `key_name`, `key_label`, `status`, `description`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952625010012741633, 'fangkuan', '放款失败率预警', b'1', '检测放款失败率预警', '2025-08-05 14:57:35', '2025-08-05 15:04:38', NULL, NULL, b'0');
INSERT INTO `t_sys_dict_value` (`id`, `key_id`, `key_name`, `status`, `value`, `label`, `description`, `sort`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952625525069078529, 1952625010012741633, 'fangkuan', b'1', 'loan_hour_successRate', '每小时放款成功率', '每小时放款成功率', NULL, '2025-08-05 14:59:38', '2025-08-05 14:59:38', NULL, NULL, b'0');
INSERT INTO `t_sys_dict_value` (`id`, `key_id`, `key_name`, `status`, `value`, `label`, `description`, `sort`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952625594879074306, 1952625010012741633, 'fangkuan', b'1', 'loan_hour_number', '每小时放款成功数量', '每小时放款成功数量', NULL, '2025-08-05 14:59:55', '2025-08-05 14:59:55', NULL, NULL, b'0');
INSERT INTO `t_sys_dict_value` (`id`, `key_id`, `key_name`, `status`, `value`, `label`, `description`, `sort`, `create_time`, `update_time`, `creator`, `updater`, `deleted`)
    VALUES (1952625706153959425, 1952625010012741633, 'fangkuan', b'1', 'loan_hour', '每小时放款总数', '每小时放款总数', NULL, '2025-08-05 15:00:21', '2025-08-05 15:00:21', NULL, NULL, b'0');

-- 1. 首先执行表结构变更（如果还未执行）
ALTER TABLE `t_alert_event`
    ADD COLUMN `model_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警名称（对应t_alert_model.model_name）' AFTER `model_code`,
    ADD COLUMN `business_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务类型（对应t_alert_model.business_type）' AFTER `model_name`,
    ADD COLUMN `alert_type` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警类型（对应t_alert_model.alert_type）' AFTER `business_type`,
    ADD COLUMN `system_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '系统名称（对应t_data_source.system_name）' AFTER `system_code`;

-- 2. 数据迁移：填充预警模型相关的冗余字段
UPDATE t_alert_event ae
    INNER JOIN t_alert_model am ON ae.model_code = am.model_code AND am.deleted = 0
    SET
        ae.model_name = am.model_name,
        ae.business_type = am.business_type,
        ae.alert_type = am.alert_type
WHERE
    ae.deleted = 0
  AND (ae.model_name IS NULL OR ae.business_type IS NULL OR ae.alert_type IS NULL);

-- 3. 数据迁移：填充数据源相关的冗余字段
UPDATE t_alert_event ae
    INNER JOIN t_data_source ds ON ae.system_code = ds.system_code AND ds.deleted = 0
    SET
        ae.system_name = ds.system_name
WHERE
    ae.deleted = 0
  AND ae.system_name IS NULL;

CREATE INDEX `idx_model_name` ON `t_alert_event` (`model_name`);
CREATE INDEX `idx_business_type` ON `t_alert_event` (`business_type`);
CREATE INDEX `idx_alert_type` ON `t_alert_event` (`alert_type`);
CREATE INDEX `idx_model_code` ON `t_alert_event` (`model_code`);

-- 修改预警事件结构
ALTER TABLE `t_alert_event`
    ADD COLUMN `alert_time` datetime NULL DEFAULT NULL COMMENT '上报时间' AFTER `system_name`;

UPDATE t_alert_event
SET alert_time = create_time,
    update_time = NOW()
WHERE alert_time IS NULL
  AND deleted = 0
  AND create_time IS NOT NULL
    AND create_time>'2025-07-01 00:00:00';


DROP TABLE IF EXISTS `t_ding_talk_dept`;
CREATE TABLE `t_ding_talk_dept`  (
      `id` bigint(20) NOT NULL COMMENT 'id',
      `parent_id` bigint(20) NULL DEFAULT 0 COMMENT '父部门id',
      `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门id',
      `dept_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '部门名称',
      `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
      `creator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
      `updater` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
      `deleted` bit(1) NULL DEFAULT b'0' COMMENT '是否删除 0未删除 null已删除',
      PRIMARY KEY (`id`) USING BTREE,
      UNIQUE INDEX `uniq_dept_id`(`dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '钉钉部门' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `t_ding_talk_user`;
CREATE TABLE `t_ding_talk_user`  (
      `id` bigint(20) NOT NULL COMMENT 'id',
      `user_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户id',
      `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门id',
      `dept_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '部门名称',
      `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
      `post` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '岗位',
      `mobile` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机',
      `email` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
      `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
      `creator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
      `updater` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
      `deleted` bit(1) NULL DEFAULT b'0' COMMENT '是否删除 0未删除 null已删除',
      PRIMARY KEY (`id`) USING BTREE,
      UNIQUE INDEX `uniq_dept_id_user_id`(`user_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '钉钉用户' ROW_FORMAT = Dynamic;