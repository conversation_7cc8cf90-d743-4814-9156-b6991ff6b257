<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.converge.repository.mapper.alert.AlertMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.converge.repository.domain.alert.db.AlertMessageDO">
        <id column="id" property="id" />
        <result column="event_id" property="eventId" />
        <result column="model_code" property="modelCode" />
        <result column="model_name" property="modelName" />
        <result column="alert_type" property="alertType" />
        <result column="platform_name" property="platformName" />
        <result column="period" property="period" />
        <result column="service_no" property="serviceNo" />
        <result column="business_type" property="businessType" />
        <result column="state" property="state" />
        <result column="index_value" property="indexValue" />
        <result column="reason" property="reason" />
        <result column="payload" property="payload" />
        <result column="rule_id" property="ruleId" />
        <result column="rule_name" property="ruleName" />
        <result column="related" property="related" />
        <result column="warn_level" property="warnLevel" />
        <result column="system_code" property="systemCode" />
        <result column="system_name" property="systemName" />
        <result column="alert_time" property="alertTime" />
        <result column="config_id" property="configId" />
        <result column="frequency" property="frequency" />
        <result column="warn_type" property="warnType" />
        <result column="notification_users" property="notificationUsers" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, event_id, model_code, model_name, alert_type, platform_name, period, service_no, business_type, state, index_value, reason, payload, rule_id, rule_name, related, warn_level, system_code, system_name, alert_time, config_id, frequency, warn_type, notification_users, create_time, update_time, creator, updater, deleted
    </sql>

    <select id="selectMessagePage" resultMap="BaseResultMap" parameterType="com.tool.converge.repository.domain.alert.bo.AlertMessageQueryParamsBO">
        SELECT
            am.id AS id,
            am.event_id,
            am.model_code,
            am.model_name,
            am.alert_type,
            am.platform_name,
            am.period,
            am.service_no,
            am.business_type,
            am.state,
            am.index_value,
            am.reason,
            am.payload,
            am.rule_id,
            am.rule_name,
            am.related,
            am.warn_level,
            am.system_code,
            am.system_name,
            am.alert_time,
            am.config_id,
            am.frequency,
            am.warn_type,
            am.notification_users,
            am.create_time,
            am.update_time,
            am.creator,
            am.updater,
            am.deleted
        FROM
            t_alert_message am
        <where>
            am.deleted = 0
            <if test="queryParamsBO.modelCode != null and queryParamsBO.modelCode !=''">
                AND am.model_code LIKE CONCAT('%', #{queryParamsBO.modelCode}, '%')
            </if>
            <if test="queryParamsBO.modelName != null and queryParamsBO.modelName !=''">
                AND am.model_name LIKE CONCAT('%', #{queryParamsBO.modelName}, '%')
            </if>
            <if test="queryParamsBO.alertTimeStart != null">
                AND am.alert_time &gt;= #{queryParamsBO.alertTimeStart}
            </if>
            <if test="queryParamsBO.alertTimeEnd != null">
                AND am.alert_time &lt;= #{queryParamsBO.alertTimeEnd} 
            </if>
            <if test="queryParamsBO.platformName != null and queryParamsBO.platformName !=''">
                AND am.platform_name LIKE CONCAT('%', #{queryParamsBO.platformName}, '%')
            </if>
            <if test="queryParamsBO.businessType != null and queryParamsBO.businessType !=''">
                AND am.business_type = #{queryParamsBO.businessType}
            </if>
            <if test="queryParamsBO.systemName != null and queryParamsBO.systemName !=''">
                AND am.system_name = #{queryParamsBO.systemName}
            </if>
            <if test="queryParamsBO.alertType != null and queryParamsBO.alertType !=''">
                AND am.alert_type = #{queryParamsBO.alertType}
            </if>
            <if test="queryParamsBO.warnLevel != null and queryParamsBO.warnLevel !=''">
                AND am.warn_level = #{queryParamsBO.warnLevel}
            </if>
        </where>
        <choose>
            <when test="queryParamsBO.orderFields != null and queryParamsBO.orderFields !=''">
                ORDER BY ${queryParamsBO.orderFields} ${queryParamsBO.orderRules}
            </when>
            <otherwise>
                ORDER BY am.alert_time DESC, am.id DESC
            </otherwise>
        </choose>   
    </select>

</mapper>
